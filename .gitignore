# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
node_modules/
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions
jspm_packages/

# testing
/coverage
coverage
*.lcov
.nyc_output

# next.js
/.next/
.next
/out/
out

# production
/build
build/Release

# misc
.DS_Store
*.pem
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# env files
.env*
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Cache directories
.npm
.eslintcache
.stylelintcache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/
.node_repl_history
.cache
.parcel-cache
.temp
.cache
**/.vitepress/cache

# Build outputs
dist
.nuxt
.vuepress/dist
**/.vitepress/dist
.docusaurus

# Other
.grunt
bower_components
.lock-wscript
*.tgz
.yarn-integrity
.serverless/
.fusebox/
.dynamodb/
.tern-port
.vscode-test
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
